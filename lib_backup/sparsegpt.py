import math
import time

import torch
import torch.nn as nn
import transformers

# 禁用TF32以确保数值精度
torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

class SparseGPT:
    """
    SparseGPT剪枝算法实现
    
    核心原理：
    1. 基于二阶泰勒展开的最优脑损伤（Optimal Brain Damage）理论
    2. 使用Hessian矩阵的逆来指导剪枝过程
    3. 在剪枝每个权重后，立即更新剩余权重以最小化输出误差
    4. 这种方法能够在保持模型性能的同时实现高稀疏度
    
    与Wanda的区别：
    - SparseGPT考虑权重间的相互作用（通过Hessian）
    - Wanda只考虑单个权重的重要性
    - SparseGPT理论上更精确但计算成本更高
    """

    def __init__(self, layer):
        """
        初始化SparseGPT剪枝器
        
        Args:
            layer: 要剪枝的神经网络层
        """
        self.layer = layer
        self.dev = self.layer.weight.device
        W = layer.weight.data.clone()
        
        # 处理不同类型的层
        if isinstance(self.layer, nn.Conv2d):
            W = W.flatten(1)
        if isinstance(self.layer, transformers.Conv1D):
            W = W.t()
            
        self.rows = W.shape[0]     # 输出维度
        self.columns = W.shape[1]  # 输入维度
        
        # 初始化Hessian矩阵（输入的二阶统计量）
        self.H = torch.zeros((self.columns, self.columns), device=self.dev)
        self.nsamples = 0

    def add_batch(self, inp, out):
        """
        添加一个批次的数据来累积Hessian统计
        
        核心计算：H += (1/n) * X^T * X
        其中X是输入激活，H是近似的Hessian矩阵
        
        Args:
            inp: 输入激活
            out: 输出激活（在SparseGPT中未使用）
        """
        if len(inp.shape) == 2:
            inp = inp.unsqueeze(0)
        tmp = inp.shape[0]
        
        # 处理不同类型的层
        if isinstance(self.layer, nn.Linear) or isinstance(self.layer, transformers.Conv1D):
            if len(inp.shape) == 3:
                inp = inp.reshape((-1, inp.shape[-1]))
            inp = inp.t()
            
        # 更新样本数量权重
        self.H *= self.nsamples / (self.nsamples + tmp)
        self.nsamples += tmp
        
        # 归一化并累积Hessian
        inp = math.sqrt(2 / self.nsamples) * inp.float()
        self.H += inp.matmul(inp.t())

    def fasterprune(
        self, sparsity, prune_n=0, prune_m=0, blocksize=128, percdamp=.01
    ):
        """
        执行快速SparseGPT剪枝
        
        核心算法步骤：
        1. 计算Hessian逆矩阵（使用Cholesky分解）
        2. 逐块处理权重，对每个权重：
           a. 根据重要性分数决定是否剪枝
           b. 如果剪枝，计算对其他权重的影响
           c. 更新剩余权重以补偿剪枝的影响
        3. 这确保了在剪枝过程中输出尽可能接近原始输出
        
        Args:
            sparsity: 目标稀疏度
            prune_n, prune_m: N:M结构化稀疏参数
            blocksize: 分块大小，用于内存优化
            percdamp: 阻尼系数，用于数值稳定
        """
        W = self.layer.weight.data.clone()
        if isinstance(self.layer, nn.Conv2d):
            W = W.flatten(1)
        if isinstance(self.layer, transformers.Conv1D):
            W = W.t()
        W = W.float()

        tick = time.time()

        H = self.H
        del self.H
        
        # 处理死神经元（对角线为0的情况）
        dead = torch.diag(H) == 0
        H[dead, dead] = 1
        W[:, dead] = 0

        Losses = torch.zeros(self.rows, device=self.dev)

        # 添加阻尼项以提高数值稳定性
        damp = percdamp * torch.mean(torch.diag(H))
        diag = torch.arange(self.columns, device=self.dev)
        H[diag, diag] += damp
        
        # 计算Hessian的逆矩阵（使用Cholesky分解）
        H = torch.linalg.cholesky(H)
        H = torch.cholesky_inverse(H)
        H = torch.linalg.cholesky(H, upper=True)
        Hinv = H

        mask = None

        # 分块处理以优化内存使用
        for i1 in range(0, self.columns, blocksize):
            i2 = min(i1 + blocksize, self.columns)
            count = i2 - i1

            W1 = W[:, i1:i2].clone()
            Q1 = torch.zeros_like(W1)      # 量化后的权重
            Err1 = torch.zeros_like(W1)    # 误差项
            Losses1 = torch.zeros_like(W1) # 损失
            Hinv1 = Hinv[i1:i2, i1:i2]

            # 根据稀疏度类型计算剪枝掩码
            if prune_n == 0: 
                # 非结构化稀疏：基于Hessian对角线计算重要性分数
                if mask is not None:
                    mask1 = mask[:, i1:i2]
                else:
                    # 重要性分数 = 权重^2 / Hessian对角线^2
                    tmp = W1 ** 2 / (torch.diag(Hinv1).reshape((1, -1))) ** 2
                    thresh = torch.sort(tmp.flatten())[0][int(tmp.numel() * sparsity)]
                    mask1 = tmp <= thresh
            else:
                # 结构化稀疏：初始化掩码
                mask1 = torch.zeros_like(W1) == 1

            # 逐个处理权重
            for i in range(count):
                w = W1[:, i]         # 当前权重列
                d = Hinv1[i, i]      # Hessian逆对角线元素

                # 对于N:M稀疏，每M个元素中选择N个进行剪枝
                if prune_n != 0 and i % prune_m == 0:
                    tmp = W1[:, i:(i + prune_m)] ** 2 / (torch.diag(Hinv1)[i:(i + prune_m)].reshape((1, -1))) ** 2
                    mask1.scatter_(1, i + torch.topk(tmp, prune_n, dim=1, largest=False)[1], True)

                # 应用剪枝掩码
                q = w.clone()
                q[mask1[:, i]] = 0

                Q1[:, i] = q
                # 计算剪枝损失
                Losses1[:, i] = (w - q) ** 2 / d ** 2

                # === SparseGPT的核心：权重更新公式 ===
                # 计算剪枝误差对其他权重的影响
                err1 = (w - q) / d 
                # 更新当前块中剩余的权重
                W1[:, i:] -= err1.unsqueeze(1).matmul(Hinv1[i, i:].unsqueeze(0))
                Err1[:, i] = err1

            # 更新权重矩阵
            W[:, i1:i2] = Q1
            Losses += torch.sum(Losses1, 1) / 2

            # 更新剩余块的权重（跨块影响）
            W[:, i2:] -= Err1.matmul(Hinv[i1:i2, i2:])

        torch.cuda.synchronize()
        
        # 恢复原始权重形状并更新层权重
        if isinstance(self.layer, transformers.Conv1D):
            W = W.t()
        self.layer.weight.data = W.reshape(self.layer.weight.shape).to(self.layer.weight.data.dtype)

    def free(self):
        """释放内存"""
        self.H = None
        del self.H
        torch.cuda.empty_cache()