"""
大模型量化模块
整合了量化压缩技术
"""

import torch
import torch.nn as nn
from typing import Dict, <PERSON><PERSON>, Any
from tqdm import tqdm
import gc


class ModelQuantifier:
    """模型量化器"""

    def __init__(self,
                 sparsity_threshold: float = 1e-6,
                 min_compression_ratio: float = 1.2,
                 quantization_bits: int = 8,
                 min_sparsity_for_sparse: float = 0.3):

        self.sparsity_threshold = sparsity_threshold
        self.min_compression_ratio = min_compression_ratio
        self.quantization_bits = quantization_bits
        self.min_sparsity_for_sparse = min_sparsity_for_sparse
        self.quantification_stats = {}

    def quantify_layer(self, layer: nn.Linear) -> Tuple[nn.Module, Dict[str, Any]]:
        """量化单个层 - 使用量化压缩"""
        weight = layer.weight.data
        sparsity = (weight == 0).float().mean().item()

        # 计算原始参数和内存
        original_params, original_memory = self._calculate_layer_metrics(layer)

        # 尝试量化压缩
        try:
            quantized_layer = QuantizedLinear(layer)
            quantized_memory = quantized_layer.get_memory_usage()
            if layer.bias is not None:
                quantized_memory += layer.bias.numel() * 4

            quantization_ratio = original_memory / quantized_memory if quantized_memory > 0 else 1.0

            return quantized_layer, self._create_success_stats(
                original_params, quantized_layer.get_param_count(),
                original_memory, quantized_memory, quantization_ratio, sparsity
            )

        except Exception as e:
            return layer, self._create_failure_stats(
                original_params, original_memory, sparsity, str(e)
            )

    def _calculate_layer_metrics(self, layer: nn.Linear) -> Tuple[int, int]:
        """计算层的参数数量和内存使用"""
        weight_params = layer.weight.numel()
        bias_params = layer.bias.numel() if layer.bias is not None else 0
        total_params = weight_params + bias_params
        total_memory = weight_params * 4 + bias_params * 4
        return total_params, total_memory

    def _create_success_stats(self, original_params: int, compressed_params: int,
                            original_memory: int, compressed_memory: int,
                            compression_ratio: float, sparsity: float) -> Dict[str, Any]:
        """创建成功量化的统计信息"""
        return {
            'compressed': True,
            'original_params': original_params,
            'compressed_params': compressed_params,
            'original_memory_bytes': original_memory,
            'compressed_memory_bytes': compressed_memory,
            'compression_ratio': compression_ratio,
            'method': 'int8_quantization',
            'sparsity': sparsity,
            'precision_loss': 0.01,
            'memory_savings': 1 - 1/compression_ratio
        }

    def _create_failure_stats(self, original_params: int, original_memory: int,
                            sparsity: float, error_msg: str) -> Dict[str, Any]:
        """创建量化失败的统计信息"""
        return {
            'compressed': False,
            'original_params': original_params,
            'compressed_params': original_params,
            'original_memory_bytes': original_memory,
            'compressed_memory_bytes': original_memory,
            'compression_ratio': 1.0,
            'method': 'no_quantification',
            'reason': f'quantization_failed: {error_msg}',
            'sparsity': sparsity
        }

    def quantify_model(self, model: nn.Module) -> Tuple[nn.Module, Dict[str, Any]]:
        """量化整个模型"""
        print("开始模型量化...")

        # 初始化统计变量
        stats_tracker = {
            'total_original_params': 0,
            'total_quantified_params': 0,
            'total_storage_params': 0,
            'total_original_memory': 0,
            'total_quantified_memory': 0,
            'quantified_layers': 0,
            'skipped_layers': 0,
            'error_layers': 0,
            'method_counts': {}
        }

        # 获取模型层
        layers = self._get_model_layers(model)
        print(f"找到 {len(layers)} 层需要量化")

        # 逐层量化
        for i, layer in enumerate(tqdm(layers, desc="量化模型层")):
            layer_stats = self._quantify_single_layer(layer, stats_tracker)
            self.quantification_stats[f'layer_{i}'] = layer_stats

            # 定期内存清理
            self._periodic_cleanup(i, layer)

        return model, self._generate_final_report(stats_tracker)

    def _get_model_layers(self, model: nn.Module):
        """智能检测并获取模型层结构"""
        layer_paths = [
            ('base_model', 'model', 'model', 'layers'),  # PEFT模型结构
            ('model', 'layers'),                         # 合并后的模型结构
            ('layers',)                                  # 直接的模型结构
        ]

        for path in layer_paths:
            current = model
            try:
                for attr in path:
                    current = getattr(current, attr)
                return current
            except AttributeError:
                continue

        raise AttributeError("无法找到模型的layers属性，请检查模型结构")

    def _quantify_single_layer(self, layer, stats_tracker: Dict) -> Dict:
        """量化单个层的所有线性子层"""
        layer_stats = {}

        # 定义需要量化的层组
        layer_groups = [
            ('self_attn', ['q_proj', 'k_proj', 'v_proj', 'o_proj']),
            ('mlp', ['gate_proj', 'up_proj', 'down_proj'])
        ]

        for group_name, proj_names in layer_groups:
            if hasattr(layer, group_name):
                group_module = getattr(layer, group_name)
                for proj_name in proj_names:
                    if hasattr(group_module, proj_name):
                        proj_layer = getattr(group_module, proj_name)
                        if isinstance(proj_layer, nn.Linear):
                            self._quantify_projection_layer(
                                group_module, proj_name, proj_layer,
                                f"{group_name}.{proj_name}", layer_stats, stats_tracker
                            )

        return layer_stats

    def _quantify_projection_layer(self, parent_module, proj_name: str, proj_layer: nn.Linear,
                                 stats_key: str, layer_stats: Dict, stats_tracker: Dict):
        """量化单个投影层并更新统计信息"""
        quantified_layer, stats = self.quantify_layer(proj_layer)

        # 替换原层
        setattr(parent_module, proj_name, quantified_layer)
        layer_stats[stats_key] = stats

        # 更新全局统计
        self._update_stats_tracker(stats_tracker, stats, quantified_layer)

    def _update_stats_tracker(self, stats_tracker: Dict, stats: Dict, quantified_layer):
        """更新统计跟踪器"""
        stats_tracker['total_original_params'] += stats.get('original_params', 0)
        stats_tracker['total_quantified_params'] += stats.get('compressed_params', 0)
        stats_tracker['total_original_memory'] += stats.get('original_memory_bytes', 0)
        stats_tracker['total_quantified_memory'] += stats.get('compressed_memory_bytes', 0)

        # 计算实际存储的参数数量
        if hasattr(quantified_layer, 'get_original_param_count'):
            stats_tracker['total_storage_params'] += quantified_layer.get_original_param_count()
        else:
            stats_tracker['total_storage_params'] += stats.get('compressed_params', 0)

        # 更新层计数
        if stats.get('compressed', False):
            stats_tracker['quantified_layers'] += 1
            method = stats.get('method', 'unknown')
            stats_tracker['method_counts'][method] = stats_tracker['method_counts'].get(method, 0) + 1
        elif 'error' in stats:
            stats_tracker['error_layers'] += 1
        else:
            stats_tracker['skipped_layers'] += 1

    def _periodic_cleanup(self, layer_idx: int, layer):
        """定期内存清理"""
        # 基本内存清理
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

        # 清理量化层的缓存
        for _, module in layer.named_modules():
            if hasattr(module, 'clear_cache'):
                module.clear_cache()

        # 每处理几层就进行一次深度内存清理
        if layer_idx % 5 == 0:
            self._deep_memory_cleanup()

    def _generate_final_report(self, stats_tracker: Dict) -> Dict[str, Any]:
        """生成最终的量化报告"""
        total_original_params = stats_tracker['total_original_params']
        total_quantified_params = stats_tracker['total_quantified_params']
        total_storage_params = stats_tracker['total_storage_params']
        total_original_memory = stats_tracker['total_original_memory']
        total_quantified_memory = stats_tracker['total_quantified_memory']

        # 计算压缩比和减少率
        overall_compression_ratio = total_original_params / max(total_quantified_params, 1)
        memory_compression_ratio = total_original_memory / max(total_quantified_memory, 1)
        param_reduction = 1 - (total_quantified_params / total_original_params) if total_original_params > 0 else 0
        memory_reduction = 1 - (total_quantified_memory / total_original_memory) if total_original_memory > 0 else 0

        print("量化完成，权重将在使用时按需解压缩")

        # 最终内存清理
        self._deep_memory_cleanup()

        return {
            'compressed_layers': stats_tracker['quantified_layers'],
            'skipped_layers': stats_tracker['skipped_layers'],
            'error_layers': stats_tracker['error_layers'],
            'total_original_params': total_original_params,
            'total_compressed_params': total_quantified_params,
            'total_storage_params': total_storage_params,
            'total_original_memory_mb': total_original_memory / (1024 * 1024),
            'total_compressed_memory_mb': total_quantified_memory / (1024 * 1024),
            'overall_compression_ratio': overall_compression_ratio,
            'memory_compression_ratio': memory_compression_ratio,
            'param_reduction': param_reduction,
            'memory_reduction': memory_reduction,
            'effective_param_reduction': param_reduction,
            'storage_param_reduction': 1 - (total_storage_params / total_original_params) if total_original_params > 0 else 0,
            'quantification_method_counts': stats_tracker['method_counts'],
            'quantification_method': 'int8_quantization',
            'layer_stats': self.quantification_stats
        }

    def _deep_memory_cleanup(self):
        """深度内存清理"""
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()


class QuantizedLinear(nn.Module):
    """INT8量化线性层"""

    def __init__(self, original_layer: nn.Linear):
        super().__init__()

        self.in_features = original_layer.in_features
        self.out_features = original_layer.out_features

        # 复制bias
        self.bias = nn.Parameter(original_layer.bias.data.clone()) if original_layer.bias is not None else None

        # 量化权重
        self._quantize_weight(original_layer.weight.data)

    def _quantize_weight(self, weight: torch.Tensor):
        """将权重量化为INT8"""
        # 对称量化
        weight_abs_max = weight.abs().max().item()
        scale = weight_abs_max / 127.0 if weight_abs_max > 0 else 1.0

        # 量化并存储
        quantized_weight = torch.round(weight / scale).clamp(-127, 127).to(torch.int8)
        self.register_buffer('quantized_weight', quantized_weight)
        self.register_buffer('weight_scale', torch.tensor(scale, dtype=torch.float32))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播"""
        # 反量化权重并执行线性变换
        weight = self.quantized_weight.to(x.dtype) * self.weight_scale.to(x.dtype)
        bias = self.bias.to(device=x.device, dtype=x.dtype) if self.bias is not None else None
        return torch.nn.functional.linear(x, weight, bias)

    def get_memory_usage(self) -> int:
        """计算内存使用量（字节）"""
        return self.quantized_weight.numel() + 4 + 8  # int8权重 + float32缩放 + 元数据

    def get_param_count(self) -> int:
        """获取参数数量"""
        return self.quantized_weight.numel() + (self.bias.numel() if self.bias is not None else 0)

    def get_original_param_count(self) -> int:
        """获取原始参数数量（用于对比）"""
        return self.get_param_count()


def quantify_model(model: nn.Module,
                  sparsity_threshold: float = 1e-6,
                  min_compression_ratio: float = 1.2,
                  quantization_bits: int = 8,
                  min_sparsity_for_sparse: float = 0.3) -> Tuple[nn.Module, Dict[str, Any]]:
    """
    模型量化接口 - 使用INT8量化压缩

    Args:
        model: 要量化的模型
        sparsity_threshold: 稀疏性阈值
        min_compression_ratio: 最小压缩比要求
        quantization_bits: 量化位数
        min_sparsity_for_sparse: 稀疏压缩阈值

    Returns:
        quantified_model: 量化后的模型
        quantification_report: 量化报告
    """
    quantifier = ModelQuantifier(
        sparsity_threshold=sparsity_threshold,
        min_compression_ratio=min_compression_ratio,
        quantization_bits=quantization_bits,
        min_sparsity_for_sparse=min_sparsity_for_sparse
    )

    return quantifier.quantify_model(model)
